package com.xiaozhi.dialogue.llm;

import com.xiaozhi.utils.EmojiUtils;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.regex.*;

@Slf4j
public class SentenceProcessor {
    // 句子结束标点符号模式（中英文句号、感叹号、问号）
    private static final Pattern SENTENCE_END_PATTERN = Pattern.compile("[。！？!?]");

    // 逗号、分号等停顿标点
    private static final Pattern PAUSE_PATTERN = Pattern.compile("[，、；,;]");

    // 冒号和引号等特殊标点
    private static final Pattern SPECIAL_PATTERN = Pattern.compile("[：:\"]");

    // 换行符
    private static final Pattern NEWLINE_PATTERN = Pattern.compile("[\n\r]");

    // 数字模式（用于检测小数点是否在数字中）
    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\d+\\.\\d+");

    private static final int MIN_SENTENCE_LENGTH = 5; // Reduced for demo purposes

    public static class SentenceContext {
        final StringBuilder currentSentence = new StringBuilder();
        final StringBuilder remainToken = new StringBuilder(); // 剩余token缓冲区
        boolean shouldSplit = false;
        String currentToken = ""; // 当前正在处理的token

        public boolean shouldSplit() {
            return shouldSplit;
        }

        public void reset() {
            shouldSplit = false;
        }
    }

    public static SentenceContext processToken(SentenceContext context, String token) {
        // 重置断句标记
        context.reset();
        context.currentToken = token;
        log.debug("LLM Token is {}", token);

        // 如果有剩余的token，先处理它
        String fullToken = context.remainToken.toString() + token;
        context.remainToken.setLength(0); // 清空剩余token缓冲区

        for (int i = 0; i < fullToken.length(); ) {
            var codePoint = fullToken.codePointAt(i);
            var charStr = new String(Character.toChars(codePoint));

            // 将字符添加到当前句子缓冲区
            context.currentSentence.append(charStr);

            // 检查各种断句标记
            boolean isEndMark = SENTENCE_END_PATTERN.matcher(charStr).find();
            boolean isPauseMark = PAUSE_PATTERN.matcher(charStr).find();
            boolean isSpecialMark = SPECIAL_PATTERN.matcher(charStr).find();
            boolean isNewline = NEWLINE_PATTERN.matcher(charStr).find();
            boolean isEmoji = EmojiUtils.isEmoji(codePoint);

            // 检查当前句子是否包含颜文字
            boolean containsKaomoji = false;
            if (context.currentSentence.length() >= 3) { // 颜文字至少需要3个字符
                containsKaomoji = EmojiUtils.containsKaomoji(context.currentSentence.toString());
            }

            // 如果当前字符是句号，检查它是否是数字中的小数点
            if (isEndMark && charStr.equals(".")) {
                var numberString = context.currentSentence.toString();
                var numberMatcher = NUMBER_PATTERN.matcher(numberString);
                // 如果找到数字模式（如"0.271"），则不视为句子结束标点
                if (numberMatcher.find() && numberMatcher.end() >= numberString.length() - 3) {
                    isEndMark = false;
                }
            }

            // 判断是否应该发送当前句子
            boolean shouldSplit = false;
            if (isEndMark) {
                // 句子结束标点是强断句信号
                shouldSplit = true;
            } else if (isNewline) {
                // 换行符也是强断句信号
                shouldSplit = true;
            } else if ((isPauseMark || isSpecialMark || isEmoji || containsKaomoji)
                    && context.currentSentence.length() >= MIN_SENTENCE_LENGTH) {
                // 停顿标点、特殊标点、表情符号或颜文字在句子足够长时可以断句
                shouldSplit = true;
            }

            if (shouldSplit) {
                log.debug("Sentence is {}", context.currentSentence);
                context.shouldSplit = true;
                // 保存剩余的token到下一个context
                if (i + Character.charCount(codePoint) < fullToken.length()) {
                    context.remainToken.append(fullToken.substring(i + Character.charCount(codePoint)));
                }
                break;
            }

            // 移动到下一个码点
            i += Character.charCount(codePoint);
        }

        return context;
    }

    /**
     * 有状态的句子处理器，用于正确处理remainToken
     */
    public static class StatefulSentenceProcessor {
        private final StringBuilder currentSentence = new StringBuilder();
        private final StringBuilder remainToken = new StringBuilder();

        public String processToken(String token) {
            log.debug("LLM Token is {}", token);

            // 如果有剩余的token，先处理它
            String fullToken = remainToken.toString() + token;
            remainToken.setLength(0); // 清空剩余token缓冲区

            for (int i = 0; i < fullToken.length(); ) {
                var codePoint = fullToken.codePointAt(i);
                var charStr = new String(Character.toChars(codePoint));

                // 将字符添加到当前句子缓冲区
                currentSentence.append(charStr);

                // 检查各种断句标记
                boolean isEndMark = SENTENCE_END_PATTERN.matcher(charStr).find();
                boolean isPauseMark = PAUSE_PATTERN.matcher(charStr).find();
                boolean isSpecialMark = SPECIAL_PATTERN.matcher(charStr).find();
                boolean isNewline = NEWLINE_PATTERN.matcher(charStr).find();
                boolean isEmoji = EmojiUtils.isEmoji(codePoint);

                // 检查当前句子是否包含颜文字
                boolean containsKaomoji = false;
                if (currentSentence.length() >= 3) { // 颜文字至少需要3个字符
                    containsKaomoji = EmojiUtils.containsKaomoji(currentSentence.toString());
                }

                // 如果当前字符是句号，检查它是否是数字中的小数点
                if (isEndMark && charStr.equals(".")) {
                    var numberString = currentSentence.toString();
                    var numberMatcher = NUMBER_PATTERN.matcher(numberString);
                    // 如果找到数字模式（如"0.271"），则不视为句子结束标点
                    if (numberMatcher.find() && numberMatcher.end() >= numberString.length() - 3) {
                        isEndMark = false;
                    }
                }

                // 判断是否应该发送当前句子
                boolean shouldSplit = false;
                if (isEndMark) {
                    // 句子结束标点是强断句信号
                    shouldSplit = true;
                } else if (isNewline) {
                    // 换行符也是强断句信号
                    shouldSplit = true;
                } else if ((isPauseMark || isSpecialMark || isEmoji || containsKaomoji)
                        && currentSentence.length() >= MIN_SENTENCE_LENGTH) {
                    // 停顿标点、特殊标点、表情符号或颜文字在句子足够长时可以断句
                    shouldSplit = true;
                }

                if (shouldSplit) {
                    String sentence = currentSentence.toString().trim();
                    log.debug("Sentence is {}", sentence);

                    // 清空当前句子缓冲区
                    currentSentence.setLength(0);

                    // 保存剩余的token
                    if (i + Character.charCount(codePoint) < fullToken.length()) {
                        remainToken.append(fullToken.substring(i + Character.charCount(codePoint)));
                    }

                    return sentence;
                }

                // 移动到下一个码点
                i += Character.charCount(codePoint);
            }

            // 没有断句，返回null
            return null;
        }

        /**
         * 获取最后的句子（在流结束时调用）
         */
        public String getLastSentence() {
            String sentence = (remainToken.toString() + currentSentence.toString()).trim();
            if (!sentence.isEmpty()) {
                log.info("Last Sentence is {}", sentence);
                return sentence;
            }
            return null;
        }
    }
}